<%
# Copyright (C) 2011 - present Instructure, Inc.
#
# This file is part of Canvas.
#
# Canvas is free software: you can redistribute it and/or modify it under
# the terms of the GNU Affero General Public License as published by the Free
# Software Foundation, version 3 of the License.
#
# Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
# WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
# A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
# details.
#
# You should have received a copy of the GNU Affero General Public License along
# with this program. If not, see <http://www.gnu.org/licenses/>.
%>

<%
  if k5_user?
    dashboard_title = t('Home')
  else
    dashboard_title = t('Dashboard')
  end
%>

<% provide :page_title do %><%= dashboard_title %><% end %>
<%
  is_observer = @current_user&.roles(@domain_root_account)&.include?('observer')
  if show_planner?
    # fire off these `fetch` requests now so they are ready sooner
    six_months_ago = CGI.escape Time.now.utc.at_beginning_of_day.months_ago(6).iso8601(3)
    beginning_of_day = CGI.escape Time.zone.now.at_beginning_of_day.utc.iso8601(3)
    beginning_of_week = CGI.escape Time.zone.now.beginning_of_week(:sunday).utc.iso8601(3)
    end_of_week = CGI.escape Time.zone.now.end_of_week(:sunday).utc.iso8601(3)
    next_year = CGI.escape Time.zone.now.utc.at_beginning_of_day.years_since(1).iso8601(3)
    last_year = CGI.escape Time.zone.now.utc.at_beginning_of_day.years_ago(1).iso8601(3)
    if !is_observer || @cards_prefetch_observed_param == @current_user.id
      if k5_user?
%>
        <%if !Account.site_admin.feature_enabled?(:dashboard_graphql_integration) %>
          <%= prefetch_xhr("/api/v1/dashboard/dashboard_cards") %>
        <% end %>
        <%= prefetch_xhr("/api/v1/planner/items?start_date=#{last_year}&order=asc&per_page=1") %>
        <%= prefetch_xhr("/api/v1/planner/items?end_date=#{next_year}&order=desc&per_page=1") %>
        <%= prefetch_xhr("/api/v1/planner/items?start_date=#{beginning_of_week}&end_date=#{end_of_week}&per_page=100") %>
        <%= prefetch_xhr('/api/v1/users/self/missing_submissions?include%5B%5D=planner_overrides&filter%5B%5D=current_grading_period&filter%5B%5D=submittable&per_page=100') %>
      <% else %>
        <%if !Account.site_admin.feature_enabled?(:dashboard_graphql_integration) %>
          <%= prefetch_xhr("/api/v1/dashboard/dashboard_cards") %>
        <% end %>
        <%= prefetch_xhr("/api/v1/planner/items?start_date=#{six_months_ago}&filter=new_activity&order=asc") %>
        <%= prefetch_xhr("/api/v1/planner/items?end_date=#{beginning_of_day}&order=desc&per_page=1") %>
        <%= prefetch_xhr("/api/v1/planner/items?start_date=#{beginning_of_day}") %>
        <%= prefetch_xhr('/api/v1/users/self/missing_submissions?include%5B%5D=planner_overrides&filter%5B%5D=current_grading_period&filter%5B%5D=submittable') %>
      <% end %>
    <% else  #is_observer, self is not selected %>
      <% observed_course_ids = @current_user.cached_course_ids_for_observed_user(@selected_observed_user)
        .sort {|a, b| a <=> b }
        .map {|cid|  "course_ids%5B%5D=#{cid}" }
        .join('&')

if k5_user?
      %>
        <%if !Account.site_admin.feature_enabled?(:dashboard_graphql_integration) %>
          <%= prefetch_xhr("/api/v1/dashboard/dashboard_cards?observed_user_id=#{@cards_prefetch_observed_param}") %>
        <% end %>
        <%= prefetch_xhr("/api/v1/planner/items?start_date=#{last_year}&include%5B%5D=account_calendars&include%5B%5D=all_courses&order=asc&per_page=1&observed_user_id=#{@cards_prefetch_observed_param}") %>
        <%= prefetch_xhr("/api/v1/planner/items?end_date=#{next_year}&include%5B%5D=account_calendars&include%5B%5D=all_courses&order=desc&per_page=1&observed_user_id=#{@cards_prefetch_observed_param}") %>
        <%= prefetch_xhr("/api/v1/planner/items?start_date=#{beginning_of_week}&end_date=#{end_of_week}&include%5B%5D=account_calendars&include%5B%5D=all_courses&per_page=100&observed_user_id=#{@cards_prefetch_observed_param}") %>
        <%= prefetch_xhr("/api/v1/users/self/missing_submissions?include%5B%5D=planner_overrides&filter%5B%5D=current_grading_period&filter%5B%5D=submittable&per_page=100&observed_user_id=#{@cards_prefetch_observed_param}&#{observed_course_ids}") %>
      <% else %>
        <%if !Account.site_admin.feature_enabled?(:dashboard_graphql_integration) %>
          <%= prefetch_xhr("/api/v1/dashboard/dashboard_cards?observed_user_id=#{@cards_prefetch_observed_param}") %>
        <% end %>
        <%= prefetch_xhr("/api/v1/planner/items?start_date=#{six_months_ago}&include%5B%5D=account_calendars&include%5B%5D=all_courses&filter=new_activity&order=asc&observed_user_id=#{@cards_prefetch_observed_param}") %>
        <%= prefetch_xhr("/api/v1/planner/items?end_date=#{beginning_of_day}&include%5B%5D=account_calendars&include%5B%5D=all_courses&order=desc&per_page=1&observed_user_id=#{@cards_prefetch_observed_param}") %>
        <%= prefetch_xhr("/api/v1/planner/items?start_date=#{beginning_of_day}&include%5B%5D=account_calendars&include%5B%5D=all_courses&observed_user_id=#{@cards_prefetch_observed_param}") %>
        <%= prefetch_xhr("/api/v1/users/self/missing_submissions?include%5B%5D=planner_overrides&filter%5B%5D=current_grading_period&filter%5B%5D=submittable&observed_user_id=#{@cards_prefetch_observed_param}&#{observed_course_ids}") %>
      <% end %>
    <% end%>
  <% end %>

<% provide :right_side do %>
  <% if !show_planner? and !k5_user? %>
    <div class="placeholder"></div>
  <% end %>
<% end %>

<div id="dashboard" class="ic-dashboard-app">
  <h1 class="screenreader-only"><%= dashboard_title %></h1>
  <%= render :partial => 'shared/dashboard_invitation', :collection => @pending_invitations %>
  <%= render :partial => 'shared/dashboard_messages' %>
  <% if k5_user? %>
    <div id="dashboard-app-container"></div>
  <% else %>
    <% if Account.site_admin.feature_enabled?(:instui_header) 
      dashboard_header_container_props = {
        startNewCourseVisible: show_user_create_course_button(@current_user) || (@current_user.alternate_account_for_course_creation && @domain_root_account.feature_enabled?(:create_course_subaccount_picker)),
        viewGradesUrl: grades_path,
      }
    %>
      <%if dashboard_header_container_props[:startNewCourseVisible] %>
        <% if @domain_root_account.feature_enabled?(:create_course_subaccount_picker) %>
          <div id="create_course_modal_container"></div>
        <% else %>
          <%= render :partial => 'shared/new_course_form' %>
        <% end %>
      <% end %>
    <% end %>

    <div id="dashboard_header_container" class="ic-Dashboard-header" data-props="<%=dashboard_header_container_props.to_json%>"></div>
    <% if planner_enabled? %>
      <div id="dashboard-planner" class="StudentPlanner__Container" style="display: <%= show_planner? ? 'block' : 'none' %>"></div>
    <% end %>
    <div
      id="dashboard-activity"
      class="ic-Dashboard-Activity"
      style="display: <%= show_recent_activity? ? 'block' : 'none' %>"
    >
      <!-- this will be populated via xhr from UsersController::dashboard_stream_items when someone selects the stream-items dashboard option -->
    </div>

    <%= render :partial => 'shared/dashboard_card' %>
  <% end %>
</div>
